0000000000000000000000000000000000000000 ab2a25c48548a7fad8362e5c1be1432867291d7f xujiesen <<EMAIL>> 1735132331 +0800	checkout: moving from master to ab2a25c48548a7fad8362e5c1be1432867291d7f
ab2a25c48548a7fad8362e5c1be1432867291d7f ab2a25c48548a7fad8362e5c1be1432867291d7f xujiesen <<EMAIL>> 1735132344 +0800
ab2a25c48548a7fad8362e5c1be1432867291d7f 5966ab324be4fea4c3b1c2594cb8027ab1fe876d xujiesen <<EMAIL>> 1737458963 +0800	pull: Fast-forward
5966ab324be4fea4c3b1c2594cb8027ab1fe876d b469530ca4426b5eea93bcaed2790b41a72ed0d8 xujiesen <<EMAIL>> 1737459107 +0800	commit: fix(expand): build.gn修正udas名称
b469530ca4426b5eea93bcaed2790b41a72ed0d8 a2135ac84db94fd87a36d18a105c2fa80918087e xujiesen <<EMAIL>> 1739327546 +0800	pull --tags gitlab master: Fast-forward
a2135ac84db94fd87a36d18a105c2fa80918087e 084d92d7001fc9f910947abec4e7f5ad2ba46a7d xujiesen <<EMAIL>> 1739331238 +0800	commit: feat(expand): 伊格威尔定制：支持swayhigh告警显示器
084d92d7001fc9f910947abec4e7f5ad2ba46a7d fa1e13cb8ce5ac2ac06226a17661ba957ef7fbfa xujiesen <<EMAIL>> 1739861827 +0800	pull: Fast-forward
fa1e13cb8ce5ac2ac06226a17661ba957ef7fbfa dd0a9db398ac58ce104c044ca73d8746e3767a3d xujiesen <<EMAIL>> 1741056506 +0800	merge dd0a9db398ac58ce104c044ca73d8746e3767a3d: Fast-forward
dd0a9db398ac58ce104c044ca73d8746e3767a3d ec4eddf84e41e4796cf6c83ea28dcc364ab97c19 xujiesen <<EMAIL>> 1741174610 +0800	commit: feat(expand): 磁北HW2.0~2.5s告警，添加语音输出
ec4eddf84e41e4796cf6c83ea28dcc364ab97c19 ceda6c53cf6e861daa0523930e967a1de01f3f33 xujiesen <<EMAIL>> 1741764056 +0800	pull: Fast-forward
ceda6c53cf6e861daa0523930e967a1de01f3f33 7b53a4d780560a2d283a752c538f93d3eabef459 xujiesen <<EMAIL>> 1741764158 +0800	commit: fix(libprot.jtt808-1078): 三一bsd警告等级上传，配置启动
7b53a4d780560a2d283a752c538f93d3eabef459 fbf1b0db806440488cb852dd26e541504d073985 xujiesen <<EMAIL>> 1741770103 +0800	pull: Fast-forward
fbf1b0db806440488cb852dd26e541504d073985 ab44c79c707d03d71d569c99984f3be5cd0f0473 xujiesen <<EMAIL>> 1743403282 +0800	commit: fix(expand): 融盛两款告警器协议，bsd有效时间改成4秒
ab44c79c707d03d71d569c99984f3be5cd0f0473 ab44c79c707d03d71d569c99984f3be5cd0f0473 xujiesen <<EMAIL>> 1743481076 +0800	reset: moving to HEAD
ab44c79c707d03d71d569c99984f3be5cd0f0473 ab44c79c707d03d71d569c99984f3be5cd0f0473 xujiesen <<EMAIL>> 1743493294 +0800	reset: moving to HEAD
ab44c79c707d03d71d569c99984f3be5cd0f0473 fbf1b0db806440488cb852dd26e541504d073985 xujiesen <<EMAIL>> 1743493303 +0800	reset: moving to HEAD^
fbf1b0db806440488cb852dd26e541504d073985 913fb578861c19b952b121cbd69a259c6d99bf52 xujiesen <<EMAIL>> 1743565043 +0800	commit: feat(hostio/gps): 支持config.ini设置gnss搜索模式
913fb578861c19b952b121cbd69a259c6d99bf52 fbf1b0db806440488cb852dd26e541504d073985 xujiesen <<EMAIL>> 1743574479 +0800	reset: moving to HEAD^
fbf1b0db806440488cb852dd26e541504d073985 fbf1b0db806440488cb852dd26e541504d073985 xujiesen <<EMAIL>> 1744342209 +0800	reset: moving to HEAD
fbf1b0db806440488cb852dd26e541504d073985 fbf1b0db806440488cb852dd26e541504d073985 xujiesen <<EMAIL>> 1744342220 +0800	reset: moving to HEAD
fbf1b0db806440488cb852dd26e541504d073985 03a56085f16da0974582337b00b614bfe9703647 xujiesen <<EMAIL>> 1744343414 +0800	merge 03a56085f16da0974582337b00b614bfe9703647: Fast-forward
03a56085f16da0974582337b00b614bfe9703647 03a56085f16da0974582337b00b614bfe9703647 xujiesen <<EMAIL>> 1744770642 +0800	reset: moving to HEAD
03a56085f16da0974582337b00b614bfe9703647 03a56085f16da0974582337b00b614bfe9703647 xujiesen <<EMAIL>> 1744856097 +0800	reset: moving to HEAD
03a56085f16da0974582337b00b614bfe9703647 03a56085f16da0974582337b00b614bfe9703647 xujiesen <<EMAIL>> 1744878667 +0800	reset: moving to HEAD
03a56085f16da0974582337b00b614bfe9703647 03a56085f16da0974582337b00b614bfe9703647 xujiesen <<EMAIL>> 1744878706 +0800	reset: moving to HEAD
03a56085f16da0974582337b00b614bfe9703647 03a56085f16da0974582337b00b614bfe9703647 xujiesen <<EMAIL>> 1744879643 +0800	reset: moving to HEAD
03a56085f16da0974582337b00b614bfe9703647 03a56085f16da0974582337b00b614bfe9703647 xujiesen <<EMAIL>> 1744879662 +0800	reset: moving to HEAD
03a56085f16da0974582337b00b614bfe9703647 03a56085f16da0974582337b00b614bfe9703647 xujiesen <<EMAIL>> 1744880150 +0800	reset: moving to HEAD
03a56085f16da0974582337b00b614bfe9703647 b00a5cfa7ab13e93bd5b89866ac4df60b3eb257b xujiesen <<EMAIL>> 1744889028 +0800	commit: feat(expand): 融盛龙安声光报警器添加音量设置
b00a5cfa7ab13e93bd5b89866ac4df60b3eb257b c2e0cefbb49cb536991ff6c25ce9b47ffd6d6efb xujiesen <<EMAIL>> 1744892964 +0800	commit: refactor(expand): eaglewill屏幕调优
c2e0cefbb49cb536991ff6c25ce9b47ffd6d6efb 3a766831c449556b80e2849e1e68d69d40eb46d4 xujiesen <<EMAIL>> 1744948050 +0800	merge 3a766831c449556b80e2849e1e68d69d40eb46d4: Fast-forward
3a766831c449556b80e2849e1e68d69d40eb46d4 cdfcf1e10792a1b69acd6d78819a17de6837772e xujiesen <<EMAIL>> 1744971294 +0800	commit: feat(hostio): gps支持通过config.ini设置定位模式
cdfcf1e10792a1b69acd6d78819a17de6837772e 16417de4b9e488d3eeaf82d61c63dec95db161d6 xujiesen <<EMAIL>> 1745227011 +0800	pull --tags gitlab master: Fast-forward
16417de4b9e488d3eeaf82d61c63dec95db161d6 6d2b0dcf60e90817e82ed349f0161201989cb19e xujiesen <<EMAIL>> 1745397344 +0800	commit: feat(expand): normalCan1Out添加左侧bsd输出支持
6d2b0dcf60e90817e82ed349f0161201989cb19e a759100b07630df020a4a90e8cd940cd931635c7 xujiesen <<EMAIL>> 1745402200 +0800	commit: feat(expand): 磁北bsd告警，使用 闪光+声音1 代替原来的 闪光
a759100b07630df020a4a90e8cd940cd931635c7 a759100b07630df020a4a90e8cd940cd931635c7 xujiesen <<EMAIL>> 1745406563 +0800	reset: moving to HEAD
a759100b07630df020a4a90e8cd940cd931635c7 2d941e780226a45bdcbe48247c529472e691ddbd xujiesen <<EMAIL>> 1745806757 +0800	pull: Fast-forward
2d941e780226a45bdcbe48247c529472e691ddbd 277c65912bce27e6f5b5fb71551a0594223b931a xujiesen <<EMAIL>> 1745824127 +0800	commit: fix(expand): expand协议fd添加互斥锁
277c65912bce27e6f5b5fb71551a0594223b931a 4a1809dc9d783cccc22bec2caf59aedc1742fb79 xujiesen <<EMAIL>> 1745824169 +0800	commit: feat(expand): 添加expand协议超时重连开关
4a1809dc9d783cccc22bec2caf59aedc1742fb79 060d8946782b77a6bbbbdfe218a522b120dad045 xujiesen <<EMAIL>> 1747227479 +0800	commit: feat(idvr.ccu): 添加连云港公交，斑马线减速子协议
060d8946782b77a6bbbbdfe218a522b120dad045 eb1d9f44ac2cc99180c8b7dcaf4c31222b7adcc8 xujiesen <<EMAIL>> 1747282074 +0800	commit: feat(expand): 添加金龙单bsd can1输出
eb1d9f44ac2cc99180c8b7dcaf4c31222b7adcc8 498c51a96480a07c78ef287f7458a7b340faf567 xujiesen <<EMAIL>> 1747289541 +0800	pull --tags gitlab master: Fast-forward
498c51a96480a07c78ef287f7458a7b340faf567 1485d2a41dfe99068878329a82231c11883c3453 xujiesen <<EMAIL>> 1747397079 +0800	commit: fix(idvr.ccu): 修复三急没有tts播报的问题
1485d2a41dfe99068878329a82231c11883c3453 eb1d9f44ac2cc99180c8b7dcaf4c31222b7adcc8 xujiesen <<EMAIL>> 1747453723 +0800	reset: moving to eb1d9f44ac2cc99180c8b7dcaf4c31222b7adcc8
eb1d9f44ac2cc99180c8b7dcaf4c31222b7adcc8 060d8946782b77a6bbbbdfe218a522b120dad045 xujiesen <<EMAIL>> 1747453736 +0800	reset: moving to eb1d9f44ac2cc99180c8b7dcaf4c31222b7adcc8^
060d8946782b77a6bbbbdfe218a522b120dad045 498c51a96480a07c78ef287f7458a7b340faf567 xujiesen <<EMAIL>> 1747472128 +0800	pull: Fast-forward
498c51a96480a07c78ef287f7458a7b340faf567 3bc95d2d28a8c29fc26aa11b7990063e3a83d8f0 xujiesen <<EMAIL>> 1747472169 +0800	cherry-pick: fix(idvr.ccu): 修复三急没有tts播报的问题
3bc95d2d28a8c29fc26aa11b7990063e3a83d8f0 52397f6da6a8a65da1aa01716edfe6d04b70fe1c xujiesen <<EMAIL>> 1747537792 +0800	commit: fix(expand): 金龙单bsd，根据客户需求修正告警等级
52397f6da6a8a65da1aa01716edfe6d04b70fe1c 52397f6da6a8a65da1aa01716edfe6d04b70fe1c xujiesen <<EMAIL>> 1747538056 +0800	reset: moving to 52397f6da6a8a65da1aa01716edfe6d04b70fe1c
52397f6da6a8a65da1aa01716edfe6d04b70fe1c 3bc95d2d28a8c29fc26aa11b7990063e3a83d8f0 xujiesen <<EMAIL>> 1747538106 +0800	reset: moving to HEAD^
3bc95d2d28a8c29fc26aa11b7990063e3a83d8f0 498c51a96480a07c78ef287f7458a7b340faf567 xujiesen <<EMAIL>> 1747538107 +0800	reset: moving to HEAD^
498c51a96480a07c78ef287f7458a7b340faf567 213021da0b4b9c1b52ea9684d4f55ceee235135a xujiesen <<EMAIL>> 1747538145 +0800	cherry-pick: fix(idvr.ccu): 修复三急没有tts播报的问题
213021da0b4b9c1b52ea9684d4f55ceee235135a b2b37225947dd7db1abfff64dd5137fd1a1fba1a xujiesen <<EMAIL>> 1747538173 +0800	commit: fix(expand): 金龙单bsd，根据客户需求修正告警等级
b2b37225947dd7db1abfff64dd5137fd1a1fba1a b2b37225947dd7db1abfff64dd5137fd1a1fba1a xujiesen <<EMAIL>> 1747826395 +0800	reset: moving to HEAD
b2b37225947dd7db1abfff64dd5137fd1a1fba1a 0279a9a6e6aec6a23d50a28967b50f0c4b0039a5 xujiesen <<EMAIL>> 1747826399 +0800	pull (start): checkout 0279a9a6e6aec6a23d50a28967b50f0c4b0039a5
0279a9a6e6aec6a23d50a28967b50f0c4b0039a5 53a196b4eee1500a0f9d5c1139c66afdc82728e2 xujiesen <<EMAIL>> 1747826399 +0800	pull (pick): fix(expand): 金龙单bsd，根据客户需求修正告警等级
53a196b4eee1500a0f9d5c1139c66afdc82728e2 53a196b4eee1500a0f9d5c1139c66afdc82728e2 xujiesen <<EMAIL>> 1747826399 +0800	pull (finish): returning to refs/heads/master
53a196b4eee1500a0f9d5c1139c66afdc82728e2 d33eb56a2850a4a5807f0fd669e67fec7e7a6f84 xujiesen <<EMAIL>> 1748258703 +0800	commit: feat(expand): f9kGPS添加mountpoint、serverUrl、serverPort自定义配置
d33eb56a2850a4a5807f0fd669e67fec7e7a6f84 d33eb56a2850a4a5807f0fd669e67fec7e7a6f84 xujiesen <<EMAIL>> 1748398316 +0800	reset: moving to HEAD
d33eb56a2850a4a5807f0fd669e67fec7e7a6f84 fa04bd3b700c01d424c8bcdfcacc075f63e6d790 xujiesen <<EMAIL>> 1748398326 +0800	pull: Fast-forward
fa04bd3b700c01d424c8bcdfcacc075f63e6d790 92204cd5e962db9f2bc42a6d977e8e32aa9dec4f xujiesen <<EMAIL>> 1748434763 +0800	merge 92204cd5e962db9f2bc42a6d977e8e32aa9dec4f: Fast-forward
92204cd5e962db9f2bc42a6d977e8e32aa9dec4f 4909ce018a65b286bd151101a2f9ee049b8219db xujiesen <<EMAIL>> 1748588174 +0800	commit: feat(expand): 上海地标添加自检
4909ce018a65b286bd151101a2f9ee049b8219db ea176c0713d6f978f4e9090c659962bea231cb8f xujiesen <<EMAIL>> 1748607610 +0800	commit: feat(idvr.ccu): 添加苏标延迟启动property（适配磁北协议）
ea176c0713d6f978f4e9090c659962bea231cb8f 7c6d1d4edf6b806def5d34d55666ee99236d4657 xujiesen <<EMAIL>> 1748609324 +0800	commit: fix(expand): 修复龙安声光告警音量设置失效
7c6d1d4edf6b806def5d34d55666ee99236d4657 8a2f9b3d06552586b56a4c5eda7e9b487cae2608 xujiesen <<EMAIL>> 1749544773 +0800	merge 8a2f9b3d06552586b56a4c5eda7e9b487cae2608: Fast-forward
8a2f9b3d06552586b56a4c5eda7e9b487cae2608 05469b80bebff518861095046e4a2ee620c437ab xujiesen <<EMAIL>> 1750159370 +0800	commit: feat(libjtt): 苏标支持setup平台查询连接状态
05469b80bebff518861095046e4a2ee620c437ab 8a2f9b3d06552586b56a4c5eda7e9b487cae2608 xujiesen <<EMAIL>> 1750159406 +0800	reset: moving to HEAD^
8a2f9b3d06552586b56a4c5eda7e9b487cae2608 95386eb6d258faadca9e3fae8cd801192256aa21 xujiesen <<EMAIL>> 1750159460 +0800	commit: feat(idvr.ccu): 苏标支持setup平台查询连接状态
95386eb6d258faadca9e3fae8cd801192256aa21 8a2f9b3d06552586b56a4c5eda7e9b487cae2608 xujiesen <<EMAIL>> 1750159820 +0800	reset: moving to HEAD^
8a2f9b3d06552586b56a4c5eda7e9b487cae2608 6976d8bf61edd7dcd4fc1889ef1b15ddae7da95f xujiesen <<EMAIL>> 1750159832 +0800	commit: feat(idvr.ccu): 苏标支持setup平台查询连接状态
6976d8bf61edd7dcd4fc1889ef1b15ddae7da95f 8a2f9b3d06552586b56a4c5eda7e9b487cae2608 xujiesen <<EMAIL>> 1750159841 +0800	reset: moving to HEAD^
8a2f9b3d06552586b56a4c5eda7e9b487cae2608 92204cd5e962db9f2bc42a6d977e8e32aa9dec4f xujiesen <<EMAIL>> 1750159850 +0800	reset: moving to HEAD^
92204cd5e962db9f2bc42a6d977e8e32aa9dec4f 92204cd5e962db9f2bc42a6d977e8e32aa9dec4f xujiesen <<EMAIL>> 1750159942 +0800	checkout: moving from master to master
92204cd5e962db9f2bc42a6d977e8e32aa9dec4f 92204cd5e962db9f2bc42a6d977e8e32aa9dec4f xujiesen <<EMAIL>> 1750159950 +0800	reset: moving to HEAD
92204cd5e962db9f2bc42a6d977e8e32aa9dec4f 8a2f9b3d06552586b56a4c5eda7e9b487cae2608 xujiesen <<EMAIL>> 1750159955 +0800	pull --tags gitlab master: Fast-forward
8a2f9b3d06552586b56a4c5eda7e9b487cae2608 c4325f84dacc6a5d6bbd672a54e18cc1bbf91c75 xujiesen <<EMAIL>> 1750160012 +0800	commit: feat(idvr.ccu): 苏标支持setup平台查询连接状态
c4325f84dacc6a5d6bbd672a54e18cc1bbf91c75 3067369488b167d705e30f4edc1b013f444e4ee7 xujiesen <<EMAIL>> 1750232770 +0800	pull --tags gitlab master: Fast-forward
3067369488b167d705e30f4edc1b013f444e4ee7 88eef8a63fc681e8cba83648b8f852ef9fca6ad2 xujiesen <<EMAIL>> 1751441663 +0800	commit: feat(hostio): 如果启用了模拟转向，选择转向就不刷新can消息超时
88eef8a63fc681e8cba83648b8f852ef9fca6ad2 3067369488b167d705e30f4edc1b013f444e4ee7 xujiesen <<EMAIL>> 1751533136 +0800	reset: moving to HEAD^
3067369488b167d705e30f4edc1b013f444e4ee7 4bd45ccd84c51e580b3e8bfe26afdf5daddd92de xujiesen <<EMAIL>> 1751533331 +0800	commit: feat(hostio): 如果启用了模拟转向，选择转向就不刷新can消息超时
4bd45ccd84c51e580b3e8bfe26afdf5daddd92de 2dbd453d54a2bd0b96cdba60f5eef30c52bc00d2 xujiesen <<EMAIL>> 1751619713 +0800	commit: feat(expand): FBSD需求
2dbd453d54a2bd0b96cdba60f5eef30c52bc00d2 3c2f4fe7d8f4e0be51fdb9b98c0241b61ba14a3a xujiesen <<EMAIL>> 1751619726 +0800	pull (start): checkout 3c2f4fe7d8f4e0be51fdb9b98c0241b61ba14a3a
3c2f4fe7d8f4e0be51fdb9b98c0241b61ba14a3a 85953c5551a509e96b4a3cb2787a4b6bad788314 xujiesen <<EMAIL>> 1751619726 +0800	pull (pick): feat(expand): FBSD需求
85953c5551a509e96b4a3cb2787a4b6bad788314 85953c5551a509e96b4a3cb2787a4b6bad788314 xujiesen <<EMAIL>> 1751619726 +0800	pull (finish): returning to refs/heads/master
85953c5551a509e96b4a3cb2787a4b6bad788314 85953c5551a509e96b4a3cb2787a4b6bad788314 xujiesen <<EMAIL>> 1751619773 +0800	checkout: moving from master to M5PRO_CIBEI_ZHONGTONGZHILI
85953c5551a509e96b4a3cb2787a4b6bad788314 85953c5551a509e96b4a3cb2787a4b6bad788314 xujiesen <<EMAIL>> 1751619790 +0800	checkout: moving from M5PRO_CIBEI_ZHONGTONGZHILI to master
85953c5551a509e96b4a3cb2787a4b6bad788314 3c2f4fe7d8f4e0be51fdb9b98c0241b61ba14a3a xujiesen <<EMAIL>> 1751619853 +0800	reset: moving to HEAD^
3c2f4fe7d8f4e0be51fdb9b98c0241b61ba14a3a 85953c5551a509e96b4a3cb2787a4b6bad788314 xujiesen <<EMAIL>> 1753971869 +0800	checkout: moving from master to M5PRO_CIBEI_ZHONGTONGZHILI
85953c5551a509e96b4a3cb2787a4b6bad788314 3c2f4fe7d8f4e0be51fdb9b98c0241b61ba14a3a xujiesen <<EMAIL>> 1753972212 +0800	checkout: moving from M5PRO_CIBEI_ZHONGTONGZHILI to master
3c2f4fe7d8f4e0be51fdb9b98c0241b61ba14a3a 3c2f4fe7d8f4e0be51fdb9b98c0241b61ba14a3a xujiesen <<EMAIL>> 1753972217 +0800	reset: moving to HEAD
3c2f4fe7d8f4e0be51fdb9b98c0241b61ba14a3a 85953c5551a509e96b4a3cb2787a4b6bad788314 xujiesen <<EMAIL>> 1753972260 +0800	checkout: moving from master to M5PRO_CIBEI_ZHONGTONGZHILI
85953c5551a509e96b4a3cb2787a4b6bad788314 3c2f4fe7d8f4e0be51fdb9b98c0241b61ba14a3a xujiesen <<EMAIL>> 1754029003 +0800	checkout: moving from M5PRO_CIBEI_ZHONGTONGZHILI to master
3c2f4fe7d8f4e0be51fdb9b98c0241b61ba14a3a 85953c5551a509e96b4a3cb2787a4b6bad788314 xujiesen <<EMAIL>> 1754029883 +0800	checkout: moving from master to M5PRO_CIBEI_ZHONGTONGZHILI
85953c5551a509e96b4a3cb2787a4b6bad788314 c5486a7543efb3a4620737d23013dc02fe520510 xujiesen <<EMAIL>> 1754030232 +0800	commit: feat(libalgo): expand不进行bsd登记修改
c5486a7543efb3a4620737d23013dc02fe520510 3c2f4fe7d8f4e0be51fdb9b98c0241b61ba14a3a xujiesen <<EMAIL>> 1754030365 +0800	checkout: moving from M5PRO_CIBEI_ZHONGTONGZHILI to master
3c2f4fe7d8f4e0be51fdb9b98c0241b61ba14a3a 3c2f4fe7d8f4e0be51fdb9b98c0241b61ba14a3a xujiesen <<EMAIL>> 1754030430 +0800	reset: moving to HEAD
3c2f4fe7d8f4e0be51fdb9b98c0241b61ba14a3a c5486a7543efb3a4620737d23013dc02fe520510 xujiesen <<EMAIL>> 1754398162 +0800	checkout: moving from master to M5PRO_CIBEI_ZHONGTONGZHILI
c5486a7543efb3a4620737d23013dc02fe520510 30d4fd90f63e5c330117d861809a1fcbf24c06b2 xujiesen <<EMAIL>> 1754400184 +0800	commit: feat(expand): 中通智利添加二级告警语音输出
30d4fd90f63e5c330117d861809a1fcbf24c06b2 3c2f4fe7d8f4e0be51fdb9b98c0241b61ba14a3a xujiesen <<EMAIL>> 1754400192 +0800	checkout: moving from M5PRO_CIBEI_ZHONGTONGZHILI to master
3c2f4fe7d8f4e0be51fdb9b98c0241b61ba14a3a 58bbc2bf275c888900a33da68d13ee55458e9642 xujiesen <<EMAIL>> 1755002123 +0800	commit: feat(rndis_host): 移植g1h2的usb dongle
